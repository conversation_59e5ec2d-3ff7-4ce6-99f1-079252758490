import { _decorator, Component, Node, Sprite, Label, Button, UITransform, resources, SpriteFrame, Color } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GeneratedScene')
export class GeneratedScene extends Component {

  start() {
    this.createUI();
  }

  createUI() {
    const canvas = this.node;

    // Create BackgroundImage
    const element0 = new Node('BackgroundImage');
    element0.setPosition(-39, -9, 0);
    const transform0 = element0.addComponent(UITransform);
    transform0.setContentSize(514, 973);
    const sprite0 = element0.addComponent(Sprite);
    // Generated image: rect_514x973_rgb(241, 39, 39)_text_BackgroundImage.png
    // Load generated image
    resources.load('textures/generated/rect_514x973_rgb(241, 39, 39)_text_BackgroundImage/spriteFrame', SpriteFrame).then((spriteFrame) => {
      sprite0.spriteFrame = spriteFrame;
    }).catch((error) => {
      console.error('Failed to load sprite frame:', error);
    });
    canvas.addChild(element0);

    // Create IconImage2
    const element1 = new Node('IconImage2');
    element1.setPosition(9, 24, 0);
    const transform1 = element1.addComponent(UITransform);
    transform1.setContentSize(107, 107);
    const sprite1 = element1.addComponent(Sprite);
    // Generated image: ellipse_107x107_rgb(217, 217, 217)_text_IconImage2.png
    // Load generated image
    resources.load('textures/generated/ellipse_107x107_rgb(217, 217, 217)_text_IconImage2/spriteFrame', SpriteFrame).then((spriteFrame) => {
      sprite1.spriteFrame = spriteFrame;
    }).catch((error) => {
      console.error('Failed to load sprite frame:', error);
    });
    canvas.addChild(element1);

    // Create IconImage1
    const element2 = new Node('IconImage1');
    element2.setPosition(126, 416, 0);
    const transform2 = element2.addComponent(UITransform);
    transform2.setContentSize(137, 123);
    const sprite2 = element2.addComponent(Sprite);
    // Generated image: ellipse_137x123_rgb(217, 217, 217)_text_IconImage1.png
    // Load generated image
    resources.load('textures/generated/ellipse_137x123_rgb(217, 217, 217)_text_IconImage1/spriteFrame', SpriteFrame).then((spriteFrame) => {
      sprite2.spriteFrame = spriteFrame;
    }).catch((error) => {
      console.error('Failed to load sprite frame:', error);
    });
    canvas.addChild(element2);

    // Create GameTitle
    const element3 = new Node('GameTitle');
    element3.setPosition(152, 224, 0);
    const transform3 = element3.addComponent(UITransform);
    transform3.setContentSize(176, 40);
    const label3 = element3.addComponent(Label);
    label3.string = 'GameTitle';
    label3.fontSize = 24;
    label3.color = new Color(0, 0, 0, 255);
    canvas.addChild(element3);

  }
}
