"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageGenerator = void 0;
const canvas_1 = require("canvas");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * Generate a simple PNG image for basic shapes
 */
class ImageGenerator {
    constructor(outputDir) {
        this.outputDir = outputDir;
        this.ensureOutputDir();
    }
    ensureOutputDir() {
        const assetsDir = path.join(this.outputDir, 'assets');
        if (!fs.existsSync(assetsDir)) {
            fs.mkdirSync(assetsDir, { recursive: true });
        }
    }
    /**
     * Generate a rectangle image
     */
    generateRectangle(props) {
        const { width, height, cornerRadius = 0, backgroundColor = '#FFFFFF', borderColor, borderWidth = 0, text, textColor = '#000000', fontSize } = props;
        const canvas = (0, canvas_1.createCanvas)(width, height);
        const ctx = canvas.getContext('2d');
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        // Draw background
        ctx.fillStyle = backgroundColor;
        if (cornerRadius > 0) {
            this.drawRoundedRect(ctx, 0, 0, width, height, cornerRadius);
            ctx.fill();
        }
        else {
            ctx.fillRect(0, 0, width, height);
        }
        // Draw border
        if (borderColor && borderWidth > 0) {
            ctx.strokeStyle = borderColor;
            ctx.lineWidth = borderWidth;
            if (cornerRadius > 0) {
                this.drawRoundedRect(ctx, borderWidth / 2, borderWidth / 2, width - borderWidth, height - borderWidth, cornerRadius);
                ctx.stroke();
            }
            else {
                ctx.strokeRect(borderWidth / 2, borderWidth / 2, width - borderWidth, height - borderWidth);
            }
        }
        // Draw text if provided
        if (text) {
            const optimalFontSize = fontSize || this.getOptimalFontSize(ctx, text, width, height);
            this.drawText(ctx, text, 0, 0, width, height, optimalFontSize, textColor);
        }
        const fileName = text
            ? `rect_${width}x${height}_${backgroundColor.replace('#', '')}_text_${text.replace(/\s+/g, '_')}.png`
            : `rect_${width}x${height}_${backgroundColor.replace('#', '')}.png`;
        return this.saveCanvas(canvas, fileName);
    }
    /**
     * Generate a circle/ellipse image
     */
    generateEllipse(props) {
        const { width, height, backgroundColor = '#FFFFFF', borderColor, borderWidth = 0, text, textColor = '#000000', fontSize } = props;
        const canvas = (0, canvas_1.createCanvas)(width, height);
        const ctx = canvas.getContext('2d');
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        // Draw background
        ctx.fillStyle = backgroundColor;
        ctx.beginPath();
        ctx.ellipse(width / 2, height / 2, width / 2, height / 2, 0, 0, 2 * Math.PI);
        ctx.fill();
        // Draw border
        if (borderColor && borderWidth > 0) {
            ctx.strokeStyle = borderColor;
            ctx.lineWidth = borderWidth;
            ctx.beginPath();
            ctx.ellipse(width / 2, height / 2, (width - borderWidth) / 2, (height - borderWidth) / 2, 0, 0, 2 * Math.PI);
            ctx.stroke();
        }
        // Draw text if provided
        if (text) {
            const optimalFontSize = fontSize || this.getOptimalFontSize(ctx, text, width, height);
            this.drawText(ctx, text, 0, 0, width, height, optimalFontSize, textColor);
        }
        const fileName = text
            ? `ellipse_${width}x${height}_${backgroundColor.replace('#', '')}_text_${text.replace(/\s+/g, '_')}.png`
            : `ellipse_${width}x${height}_${backgroundColor.replace('#', '')}.png`;
        return this.saveCanvas(canvas, fileName);
    }
    /**
     * Generate a rounded rectangle image
     */
    generateRoundedRectangle(props) {
        const { width, height, cornerRadius = Math.min(width, height) * 0.1, backgroundColor = '#FFFFFF', borderColor, borderWidth = 0, text, textColor = '#000000', fontSize } = props;
        const canvas = (0, canvas_1.createCanvas)(width, height);
        const ctx = canvas.getContext('2d');
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        // Draw background
        ctx.fillStyle = backgroundColor;
        this.drawRoundedRect(ctx, 0, 0, width, height, cornerRadius);
        ctx.fill();
        // Draw border
        if (borderColor && borderWidth > 0) {
            ctx.strokeStyle = borderColor;
            ctx.lineWidth = borderWidth;
            this.drawRoundedRect(ctx, borderWidth / 2, borderWidth / 2, width - borderWidth, height - borderWidth, cornerRadius);
            ctx.stroke();
        }
        // Draw text if provided
        if (text) {
            const optimalFontSize = fontSize || this.getOptimalFontSize(ctx, text, width, height);
            this.drawText(ctx, text, 0, 0, width, height, optimalFontSize, textColor);
        }
        const fileName = text
            ? `rounded_rect_${width}x${height}_${cornerRadius}_${backgroundColor.replace('#', '')}_text_${text.replace(/\s+/g, '_')}.png`
            : `rounded_rect_${width}x${height}_${cornerRadius}_${backgroundColor.replace('#', '')}.png`;
        return this.saveCanvas(canvas, fileName);
    }
    /**
     * Generate a gradient background
     */
    generateGradient(props) {
        const { width, height, startColor, endColor, direction = 'vertical' } = props;
        const canvas = (0, canvas_1.createCanvas)(width, height);
        const ctx = canvas.getContext('2d');
        // Create gradient
        let gradient;
        switch (direction) {
            case 'horizontal':
                gradient = ctx.createLinearGradient(0, 0, width, 0);
                break;
            case 'diagonal':
                gradient = ctx.createLinearGradient(0, 0, width, height);
                break;
            default: // vertical
                gradient = ctx.createLinearGradient(0, 0, 0, height);
        }
        gradient.addColorStop(0, startColor);
        gradient.addColorStop(1, endColor);
        // Fill with gradient
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        return this.saveCanvas(canvas, `gradient_${width}x${height}_${direction}_${startColor.replace('#', '')}_${endColor.replace('#', '')}.png`);
    }
    /**
     * Generate a simple pattern (checkerboard, stripes, etc.)
     */
    generatePattern(props) {
        const { width, height, backgroundColor = '#FFFFFF', pattern, patternColor, spacing = 20 } = props;
        const canvas = (0, canvas_1.createCanvas)(width, height);
        const ctx = canvas.getContext('2d');
        // Fill background
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, width, height);
        // Draw pattern
        ctx.fillStyle = patternColor;
        switch (pattern) {
            case 'checkerboard':
                for (let x = 0; x < width; x += spacing * 2) {
                    for (let y = 0; y < height; y += spacing * 2) {
                        ctx.fillRect(x, y, spacing, spacing);
                        ctx.fillRect(x + spacing, y + spacing, spacing, spacing);
                    }
                }
                break;
            case 'stripes':
                for (let y = 0; y < height; y += spacing * 2) {
                    ctx.fillRect(0, y, width, spacing);
                }
                break;
            case 'dots':
                for (let x = spacing; x < width; x += spacing * 2) {
                    for (let y = spacing; y < height; y += spacing * 2) {
                        ctx.beginPath();
                        ctx.arc(x, y, spacing / 4, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                }
                break;
        }
        return this.saveCanvas(canvas, `pattern_${pattern}_${width}x${height}_${backgroundColor.replace('#', '')}_${patternColor.replace('#', '')}.png`);
    }
    /**
     * Helper method to draw rounded rectangle
     */
    drawRoundedRect(ctx, x, y, width, height, radius) {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
    }
    /**
     * Helper method to draw text on canvas
     */
    drawText(ctx, text, x, y, width, height, fontSize = 16, textColor = '#000000') {
        // Set font
        ctx.font = `${fontSize}px Arial, sans-serif`;
        ctx.fillStyle = textColor;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        // Calculate text position (center of the shape)
        const centerX = x + width / 2;
        const centerY = y + height / 2;
        // Draw text
        ctx.fillText(text, centerX, centerY);
    }
    /**
     * Helper method to get optimal font size for text within bounds
     */
    getOptimalFontSize(ctx, text, maxWidth, maxHeight, maxFontSize = 24) {
        let fontSize = maxFontSize;
        ctx.font = `${fontSize}px Arial, sans-serif`;
        // Reduce font size until text fits
        while (fontSize > 8 && (ctx.measureText(text).width > maxWidth * 0.8 || fontSize > maxHeight * 0.5)) {
            fontSize -= 2;
            ctx.font = `${fontSize}px Arial, sans-serif`;
        }
        return fontSize;
    }
    /**
     * Save canvas to PNG file
     */
    saveCanvas(canvas, fileName) {
        const assetsDir = path.join(this.outputDir, 'assets');
        const filePath = path.join(assetsDir, fileName);
        const buffer = canvas.toBuffer('image/png');
        fs.writeFileSync(filePath, buffer);
        return {
            fileName,
            filePath,
            width: canvas.width,
            height: canvas.height
        };
    }
    /**
     * Extract color from Figma fills
     */
    static extractColorFromFills(fills) {
        if (!fills || fills.length === 0)
            return '#FFFFFF';
        const fill = fills[0];
        if (fill.type === 'SOLID') {
            const { r, g, b, a = 1 } = fill.color;
            const red = Math.round(r * 255);
            const green = Math.round(g * 255);
            const blue = Math.round(b * 255);
            const alpha = Math.round(a * 255);
            if (alpha < 255) {
                return `rgba(${red}, ${green}, ${blue}, ${alpha / 255})`;
            }
            return `rgb(${red}, ${green}, ${blue})`;
        }
        return '#FFFFFF';
    }
    /**
     * Extract corner radius from Figma element
     */
    static extractCornerRadius(element) {
        if (element.rectangleCornerRadii) {
            // Average the corner radii if they differ
            return Math.max(...element.rectangleCornerRadii);
        }
        return element.cornerRadius || 0;
    }
}
exports.ImageGenerator = ImageGenerator;
