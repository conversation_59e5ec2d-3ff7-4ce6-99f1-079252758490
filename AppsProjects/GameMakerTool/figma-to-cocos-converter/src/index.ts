import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';
import { mockFigmaData } from './mockData';
import { ImageGenerator, GeneratedImage } from './imageGenerator';

// Load environment variables
dotenv.config();

// Interfaces for Figma API
export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FigmaNode {
  id: string;
  name: string;
  type: string;
  children?: FigmaNode[];
  absoluteBoundingBox?: BoundingBox;
  characters?: string;
  fills?: any[];
  strokes?: any[];
}

export interface Document {
  id: string;
  name: string;
  type: string;
  children: FigmaNode[];
}

export interface FigmaFile {
  document: Document;
  components: any;
  schemaVersion: number;
  styles: any;
}

// Configuration
const FIGMA_TOKEN = process.env.FIGMA_TOKEN || 'YOUR_FIGMA_TOKEN';
const FIGMA_FILE_ID = process.argv[2] || 'YOUR_FILE_ID';
const PROJECT_NAME = process.argv[3] || FIGMA_FILE_ID; // Use custom project name or file ID
const OUTPUT_DIR = path.join(__dirname, '..', 'output', PROJECT_NAME);
const IS_TEST = process.argv.includes('--test');
const IS_ANALYZE = process.argv.includes('--analyze');

// Global image generator instance
const imageGenerator = new ImageGenerator(OUTPUT_DIR);
const generatedImages: Map<string, GeneratedImage> = new Map();

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Fetch Figma file data
async function fetchFigmaData(fileId: string, token: string): Promise<FigmaFile> {
  const url = `https://api.figma.com/v1/files/${fileId}`;
  const response = await axios.get<FigmaFile>(url, {
    headers: {
      'X-Figma-Token': token
    }
  });
  return response.data;
}

// Analyze Figma file structure and provide feedback
function analyzeFigmaStructure(figmaData: FigmaFile): void {
  console.log('\n📊 Figma文件结构分析');
  console.log('=' .repeat(50));

  const document = figmaData.document;
  console.log(`📄 文件名: ${document.name}`);
  console.log(`📂 子元素数量: ${document.children.length}`);

  let totalPages = 0;
  let totalFrames = 0;
  let totalElements = 0;
  let issues: string[] = [];
  let recommendations: string[] = [];

  document.children.forEach((page, pageIndex) => {
    if (page.type === 'CANVAS') {
      totalPages++;
      console.log(`\n📄 页面 ${pageIndex + 1}: ${page.name}`);

      if (page.children && page.children.length > 0) {
        page.children.forEach((child, childIndex) => {
          if (child.type === 'FRAME') {
            totalFrames++;
            console.log(`  🎯 场景 ${childIndex + 1}: ${child.name} (${child.type})`);

            if (child.children && child.children.length > 0) {
              totalElements += child.children.length;
              console.log(`    📦 包含 ${child.children.length} 个UI元素:`);

              child.children.forEach((element, elementIndex) => {
                const elementType = getElementTypeDescription(element);
                console.log(`      ${elementIndex + 1}. ${element.name} (${elementType})`);

                // Check for potential issues
                if (element.type === 'RECTANGLE' && element.name.toLowerCase().includes('9slice') && !element.fills) {
                  issues.push(`9-切片元素 "${element.name}" 缺少图像填充`);
                }
              });
            } else {
              issues.push(`场景 "${child.name}" 不包含任何UI元素`);
            }
          } else {
            console.log(`  ⚠️  非场景元素: ${child.name} (${child.type})`);
            recommendations.push(`考虑将 "${child.name}" 转换为FRAME以便转换为场景`);
          }
        });
      } else {
        issues.push(`页面 "${page.name}" 不包含任何元素`);
      }
    } else if (page.type === 'FRAME') {
      totalFrames++;
      console.log(`\n🎯 根级场景: ${page.name} (${page.type})`);
      recommendations.push(`建议将场景 "${page.name}" 组织到页面中以获得更好的结构`);
    } else {
      console.log(`\n⚠️  未知元素类型: ${page.name} (${page.type})`);
    }
  });

  // Summary
  console.log('\n📈 分析总结');
  console.log('=' .repeat(30));
  console.log(`📄 总页面数: ${totalPages}`);
  console.log(`🎯 总场景数: ${totalFrames}`);
  console.log(`📦 总元素数: ${totalElements}`);

  // Issues and recommendations
  if (issues.length > 0) {
    console.log('\n⚠️  发现的问题:');
    issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }

  if (recommendations.length > 0) {
    console.log('\n💡  建议改进:');
    recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
  }

  // Conversion readiness
  console.log('\n✅ 转换就绪状态');
  console.log('=' .repeat(20));

  if (totalFrames === 0) {
    console.log('❌ 无法转换: 未找到任何FRAME元素');
    console.log('   解决方案: 在Figma中创建FRAME元素来定义游戏场景');
  } else if (issues.length === 0) {
    console.log('✅ 可以转换: 结构良好');
    console.log(`   将生成 ${totalFrames} 个场景文件`);
  } else {
    console.log('⚠️  可以转换但有问题: 建议先解决上述问题');
    console.log(`   将生成 ${totalFrames} 个场景文件`);
  }

  console.log('\n🎯 支持的元素类型:');
  console.log('  • FRAME (name包含"button") → Button组件');
  console.log('  • FRAME (name包含"panel") → Panel容器');
  console.log('  • RECTANGLE/ELLIPSE → Sprite组件');
  console.log('  • TEXT → Label组件');
  console.log('  • RECTANGLE (name包含"9slice") → 9-切片Sprite');
}

// Helper function to get element type description
function getElementTypeDescription(element: FigmaNode): string {
  if (element.type === 'FRAME') {
    if (element.name.toLowerCase().includes('button')) return 'Button';
    if (element.name.toLowerCase().includes('panel')) return 'Panel';
    return 'Frame';
  }
  if (element.type === 'RECTANGLE' || element.type === 'ELLIPSE') {
    if (element.name.toLowerCase().includes('9slice') || element.name.toLowerCase().includes('nine')) {
      return '9-Slice Image';
    }
    if (element.fills && element.fills.some(fill => fill.type === 'IMAGE')) {
      return 'Image';
    }
    return 'Shape';
  }
  if (element.type === 'TEXT') return 'Text';
  return element.type;
}

// Parse Figma document and generate Cocos Creator code
function parseFigmaToCocos(figmaData: FigmaFile): Record<string, string> {
  const document = figmaData.document;
  const scenes: Record<string, string> = {};

  // Handle Pages: iterate through each page and find frames
  document.children.forEach(page => {
    if (page.type === 'CANVAS' && page.children) {
      console.log(`Processing page: ${page.name}`);

      // Process frames within each page
      page.children.forEach(child => {
        if (child.type === 'FRAME') {
          const sceneName = child.name.replace(/\s+/g, '');
          scenes[sceneName] = generateSceneCode(child);
          console.log(`Found scene: ${sceneName}`);
        }
      });
    } else if (page.type === 'FRAME') {
      // Handle case where frames are directly under document (no pages)
      const sceneName = page.name.replace(/\s+/g, '');
      scenes[sceneName] = generateSceneCode(page);
      console.log(`Found scene: ${sceneName}`);
    }
  });

  return scenes;
}

// Generate TypeScript code for a scene
function generateSceneCode(frame: FigmaNode): string {
  let hasText = false;
  let hasColor = false;

  // Check if we need additional imports
  if (frame.children) {
    frame.children.forEach(element => {
      if (element.type === 'TEXT') {
        hasText = true;
        hasColor = true;
      }
    });
  }

  let code = `import { _decorator, Component, Node, Sprite, Label, Button, UITransform, resources, SpriteFrame`;
  if (hasColor) {
    code += `, Color`;
  }
  code += ` } from 'cc';\n`;
  code += `const { ccclass, property } = _decorator;\n\n`;
  code += `@ccclass('GeneratedScene')\n`;
  code += `export class GeneratedScene extends Component {\n\n`;
  code += `  start() {\n`;
  code += `    this.createUI();\n`;
  code += `  }\n\n`;
  code += `  createUI() {\n`;
  code += `    const canvas = this.node;\n\n`;

  if (frame.children) {
    frame.children.forEach((element, index) => {
      code += generateElementCode(element, index);
    });
  }

  code += `  }\n`;
  code += `}\n`;

  return code;
}

// Generate code for individual UI elements
function generateElementCode(element: FigmaNode, index: number): string {
  let code = '';
  const nodeName = `element${index}`;
  const bbox = element.absoluteBoundingBox;

  if (!bbox) return '';

  const x = bbox.x;
  const y = bbox.y;
  const width = bbox.width;
  const height = bbox.height;

  code += `    // Create ${element.name}\n`;
  code += `    const ${nodeName} = new Node('${element.name}');\n`;
  code += `    ${nodeName}.setPosition(${x}, ${y}, 0);\n`;
  code += `    const transform${index} = ${nodeName}.addComponent(UITransform);\n`;
  code += `    transform${index}.setContentSize(${width}, ${height});\n`;

  // Handle different element types
  if (element.type === 'RECTANGLE' || element.type === 'ELLIPSE') {
    const hasImageFill = element.fills && element.fills.some(fill => fill.type === 'IMAGE');
    const isNineSlice = element.name.toLowerCase().includes('9slice') || element.name.toLowerCase().includes('nine');

    code += `    const sprite${index} = ${nodeName}.addComponent(Sprite);\n`;

    if (hasImageFill) {
      code += `    // TODO: Load image from '${element.fills?.find(fill => fill.type === 'IMAGE')?.imageRef || 'unknown'}'\n`;
    } else {
      // Generate simple shape image
      const generatedImage = generateShapeImage(element, index);
      if (generatedImage) {
        code += `    // Generated image: ${generatedImage.fileName}\n`;
        code += `    // Load generated image\n`;
        code += `    resources.load('textures/generated/${generatedImage.fileName.replace('.png', '')}/spriteFrame', SpriteFrame).then((spriteFrame) => {\n`;
        code += `      sprite${index}.spriteFrame = spriteFrame;\n`;
        code += `    }).catch((error) => {\n`;
        code += `      console.error('Failed to load sprite frame:', error);\n`;
        code += `    });\n`;
      }
    }

    if (isNineSlice) {
      code += `    // Configure as 9-slice sprite\n`;
      code += `    const spriteFrame${index} = sprite${index}.spriteFrame;\n`;
      code += `    if (spriteFrame${index}) {\n`;
      code += `      spriteFrame${index}.insetLeft = ${Math.floor(width * 0.1)};\n`;
      code += `      spriteFrame${index}.insetRight = ${Math.floor(width * 0.1)};\n`;
      code += `      spriteFrame${index}.insetTop = ${Math.floor(height * 0.1)};\n`;
      code += `      spriteFrame${index}.insetBottom = ${Math.floor(height * 0.1)};\n`;
      code += `    }\n`;
    }
  } else if (element.type === 'TEXT') {
    code += `    const label${index} = ${nodeName}.addComponent(Label);\n`;
    code += `    label${index}.string = '${element.characters || ''}';\n`;
    code += `    label${index}.fontSize = 24;\n`; // Default font size
    code += `    label${index}.color = new Color(0, 0, 0, 255);\n`; // Default black color
  } else if (element.type === 'FRAME') {
    const isButton = element.name.toLowerCase().includes('button');
    const isPanel = element.name.toLowerCase().includes('panel');

    if (isButton) {
      code += `    const button${index} = ${nodeName}.addComponent(Button);\n`;
      code += `    // TODO: Add button click event handler\n`;

      // Process child elements of the button
      if (element.children) {
        element.children.forEach((child, childIndex) => {
          const childNodeName = `${nodeName}Child${childIndex}`;
          const childX = child.absoluteBoundingBox ? child.absoluteBoundingBox.x - x : 0;
          const childY = child.absoluteBoundingBox ? child.absoluteBoundingBox.y - y : 0;
          const childWidth = child.absoluteBoundingBox ? child.absoluteBoundingBox.width : width;
          const childHeight = child.absoluteBoundingBox ? child.absoluteBoundingBox.height : height;

          code += `\n    // Button child: ${child.name}\n`;
          code += `    const ${childNodeName} = new Node('${child.name}');\n`;
          code += `    ${childNodeName}.setPosition(${childX}, ${childY}, 0);\n`;
          code += `    const childTransform${index}_${childIndex} = ${childNodeName}.addComponent(UITransform);\n`;
          code += `    childTransform${index}_${childIndex}.setContentSize(${childWidth}, ${childHeight});\n`;

          if (child.type === 'RECTANGLE' || child.type === 'ELLIPSE') {
            const hasImageFill = child.fills && child.fills.some(fill => fill.type === 'IMAGE');
            code += `    const childSprite${index}_${childIndex} = ${childNodeName}.addComponent(Sprite);\n`;
            if (hasImageFill) {
              code += `    // TODO: Load button background image from '${child.fills?.find(fill => fill.type === 'IMAGE')?.imageRef || 'unknown'}'\n`;
            }
          } else if (child.type === 'TEXT') {
            code += `    const childLabel${index}_${childIndex} = ${childNodeName}.addComponent(Label);\n`;
            code += `    childLabel${index}_${childIndex}.string = '${child.characters || ''}';\n`;
            code += `    childLabel${index}_${childIndex}.fontSize = 20;\n`;
            code += `    childLabel${index}_${childIndex}.color = new Color(255, 255, 255, 255);\n`; // Default white text
          }

          code += `    ${nodeName}.addChild(${childNodeName});\n`;
        });
      }
    } else if (isPanel) {
      code += `    // Panel container - add background if needed\n`;
      code += `    const panelSprite${index} = ${nodeName}.addComponent(Sprite);\n`;

      // Process child elements of the panel
      if (element.children) {
        element.children.forEach((child, childIndex) => {
          const childNodeName = `${nodeName}Child${childIndex}`;
          const childX = child.absoluteBoundingBox ? child.absoluteBoundingBox.x - x : 0;
          const childY = child.absoluteBoundingBox ? child.absoluteBoundingBox.y - y : 0;
          const childWidth = child.absoluteBoundingBox ? child.absoluteBoundingBox.width : width;
          const childHeight = child.absoluteBoundingBox ? child.absoluteBoundingBox.height : height;

          code += `\n    // Panel child: ${child.name}\n`;
          code += `    const ${childNodeName} = new Node('${child.name}');\n`;
          code += `    ${childNodeName}.setPosition(${childX}, ${childY}, 0);\n`;
          code += `    const childTransform${index}_${childIndex} = ${childNodeName}.addComponent(UITransform);\n`;
          code += `    childTransform${index}_${childIndex}.setContentSize(${childWidth}, ${childHeight});\n`;

          if (child.type === 'RECTANGLE' || child.type === 'ELLIPSE') {
            const hasImageFill = child.fills && child.fills.some(fill => fill.type === 'IMAGE');
            code += `    const childSprite${index}_${childIndex} = ${childNodeName}.addComponent(Sprite);\n`;
            if (hasImageFill) {
              code += `    // TODO: Load panel image from '${child.fills?.find(fill => fill.type === 'IMAGE')?.imageRef || 'unknown'}'\n`;
            }
          } else if (child.type === 'TEXT') {
            code += `    const childLabel${index}_${childIndex} = ${childNodeName}.addComponent(Label);\n`;
            code += `    childLabel${index}_${childIndex}.string = '${child.characters || ''}';\n`;
            code += `    childLabel${index}_${childIndex}.fontSize = 24;\n`;
            code += `    childLabel${index}_${childIndex}.color = new Color(0, 0, 0, 255);\n`;
          }

          code += `    ${nodeName}.addChild(${childNodeName});\n`;
        });
      }
    } else {
      code += `    // Generic frame container\n`;
      // Process child elements of generic frames
      if (element.children) {
        element.children.forEach((child, childIndex) => {
          const childNodeName = `${nodeName}Child${childIndex}`;
          const childX = child.absoluteBoundingBox ? child.absoluteBoundingBox.x - x : 0;
          const childY = child.absoluteBoundingBox ? child.absoluteBoundingBox.y - y : 0;
          const childWidth = child.absoluteBoundingBox ? child.absoluteBoundingBox.width : width;
          const childHeight = child.absoluteBoundingBox ? child.absoluteBoundingBox.height : height;

          code += `\n    // Frame child: ${child.name}\n`;
          code += `    const ${childNodeName} = new Node('${child.name}');\n`;
          code += `    ${childNodeName}.setPosition(${childX}, ${childY}, 0);\n`;
          code += `    const childTransform${index}_${childIndex} = ${childNodeName}.addComponent(UITransform);\n`;
          code += `    childTransform${index}_${childIndex}.setContentSize(${childWidth}, ${childHeight});\n`;

          if (child.type === 'RECTANGLE' || child.type === 'ELLIPSE') {
            const hasImageFill = child.fills && child.fills.some(fill => fill.type === 'IMAGE');
            code += `    const childSprite${index}_${childIndex} = ${childNodeName}.addComponent(Sprite);\n`;
            if (hasImageFill) {
              code += `    // TODO: Load image from '${child.fills?.find(fill => fill.type === 'IMAGE')?.imageRef || 'unknown'}'\n`;
            }
          } else if (child.type === 'TEXT') {
            code += `    const childLabel${index}_${childIndex} = ${childNodeName}.addComponent(Label);\n`;
            code += `    childLabel${index}_${childIndex}.string = '${child.characters || ''}';\n`;
            code += `    childLabel${index}_${childIndex}.fontSize = 24;\n`;
            code += `    childLabel${index}_${childIndex}.color = new Color(0, 0, 0, 255);\n`;
          }

          code += `    ${nodeName}.addChild(${childNodeName});\n`;
        });
      }
    }
  }

  code += `    canvas.addChild(${nodeName});\n\n`;

  return code;
}

// Generate image for shape elements
function generateShapeImage(element: FigmaNode, index: number): GeneratedImage | null {
  if (!element.absoluteBoundingBox) return null;

  const { width, height } = element.absoluteBoundingBox;
  const backgroundColor = ImageGenerator.extractColorFromFills(element.fills || []);
  const cornerRadius = ImageGenerator.extractCornerRadius(element);

  try {
    let generatedImage: GeneratedImage;

    // Common properties for all shapes
    const commonProps = {
      width: Math.round(width),
      height: Math.round(height),
      backgroundColor,
      text: element.name, // Add element name as text
      textColor: '#000000' // Default black text
    };

    if (element.type === 'ELLIPSE') {
      generatedImage = imageGenerator.generateEllipse(commonProps);
    } else if (cornerRadius > 0) {
      generatedImage = imageGenerator.generateRoundedRectangle({
        ...commonProps,
        cornerRadius
      });
    } else {
      generatedImage = imageGenerator.generateRectangle(commonProps);
    }

    generatedImages.set(element.id, generatedImage);
    console.log(`Generated image with text: ${generatedImage.fileName} for ${element.name}`);
    return generatedImage;

  } catch (error) {
    console.error(`Failed to generate image for ${element.name}:`, error);
    return null;
  }
}

// Main function
async function main(): Promise<void> {
  try {
    let figmaData: FigmaFile;

    if (IS_TEST) {
      console.log('Using mock Figma data for testing...');
      figmaData = mockFigmaData;
    } else {
      console.log('Fetching Figma data...');
      figmaData = await fetchFigmaData(FIGMA_FILE_ID, FIGMA_TOKEN);
    }

    // If analyze mode, just analyze and exit
    if (IS_ANALYZE) {
      analyzeFigmaStructure(figmaData);
      return;
    }

    console.log('Parsing and generating Cocos Creator code...');
    const scenes = parseFigmaToCocos(figmaData);
    console.log('Number of scenes found:', Object.keys(scenes).length);

    if (Object.keys(scenes).length === 0) {
      console.log('No FRAME elements found in the Figma file. Please ensure your design contains FRAME elements.');
      console.log('💡 Tip: Run with --analyze flag to see the file structure and get recommendations.');
      return;
    }

    for (const [sceneName, code] of Object.entries(scenes)) {
      const filePath = path.join(OUTPUT_DIR, `${sceneName}.ts`);
      fs.writeFileSync(filePath, code);
      console.log(`Generated ${filePath}`);
    }

    console.log('Conversion complete!');
  } catch (error) {
    console.error('Error:', (error as Error).message);
    if (!IS_TEST) {
      console.error('Stack:', (error as Error).stack);
    }
  }
}

main();