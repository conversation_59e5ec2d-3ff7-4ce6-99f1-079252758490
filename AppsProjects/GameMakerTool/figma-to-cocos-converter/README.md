# Figma 到 Cocos Creator 转换器

这个工具使用 TypeScript 将 Figma 设计转换为 Cocos Creator 场景和 UI 组件。

## 前置条件

- 已安装 Node.js
- 已全局安装 TypeScript 或通过 npm 安装
- 拥有 Figma 账户并有 API 访问权限
- Figma 个人访问令牌

## 设置

1. 克隆或下载此仓库。
2. 运行 `npm install` 安装依赖。
3. 构建项目：`npm run build`
4. 在 `.env` 文件中设置你的 Figma 令牌：
   - 复制 `.env` 文件并将 `YOUR_FIGMA_TOKEN_HERE` 替换为你的实际 Figma 个人访问令牌
   - 从以下地址获取令牌：https://www.figma.com/developers/api#access-tokens

## 使用方法

### 获取 Figma 文件 ID

1. 打开你的 Figma 文件
2. 查看浏览器地址栏中的 URL
3. 文件 ID 是 URL 中 `file/` 后面的部分
4. 例如：`https://www.figma.com/file/ABC1234567890/My-Design` 中的 `ABC1234567890` 就是文件 ID

### 运行转换器

#### 基本用法
使用你的 Figma 文件 ID 运行转换器：

```
npm start YOUR_FIGMA_FILE_ID
```

#### 指定项目名称
为不同项目创建独立的输出目录：

```
npm start YOUR_FIGMA_FILE_ID PROJECT_NAME
```

例如：
```
npm start AqUW4ZZxY1cUn0lWrPH2Uh GameTest
```

#### 分析文件结构
在使用转换之前，先分析Figma文件的结构和转换就绪状态：

```
npm run analyze YOUR_FIGMA_FILE_ID
```

或使用简写命令：
```
npm start YOUR_FIGMA_FILE_ID --analyze
```

分析功能会显示：
- 📄 文件的完整层级结构
- 🎯 可转换的场景数量
- ⚠️ 发现的问题和改进建议
- ✅ 转换就绪状态评估

#### 输出目录结构
生成的 TypeScript 文件将保存在项目特定的目录中：

```
output/
├── GameTest/           # 项目名称目录
│   ├── Homepage.ts     # 主页场景
│   ├── Settings.ts     # 设置场景
│   └── GameLevel.ts    # 游戏关卡场景
├── AnotherProject/     # 另一个项目
│   └── ...
└── AqUW4ZZxY1cUn0lWrPH2Uh/  # 使用文件ID作为目录名
    └── ...
```

如果不指定项目名称，将使用 Figma 文件 ID 作为目录名。

## 支持的控件类型及命名规则

### 场景 (Scene)
- **Figma元素类型**: `FRAME`
- **命名规则**: 任意名称（将成为场景文件名）
- **示例**: `Homepage`, `GameLevel`, `Settings`

### 图像 (Image)
- **Figma元素类型**: `RECTANGLE` 或 `ELLIPSE`
- **命名规则**: 任意名称，但建议包含描述性词语
- **特殊条件**:
  - 有图像填充 (Image Fill): 使用现有图像
  - 无图像填充: 自动生成形状图像
- **自动生成**: 支持矩形、椭圆形、圆角矩形
- **示例**: `BackgroundImage`, `CharacterImage`, `IconImage`, `LevelBackground`

### 按钮 (Button)
- **Figma元素类型**: `FRAME`
- **命名规则**: 名称中必须包含 `"button"` (不区分大小写)
- **支持的子元素**:
  - `RECTANGLE`/`ELLIPSE`: 按钮背景/图标
  - `TEXT`: 按钮文字
- **按钮类型**:
  - 完整按钮: 包含背景和文字
  - 图标按钮: 仅包含背景/图标
  - 文字按钮: 仅包含文字
- **示例**: `PlayButton`, `SettingsButton`, `AnswerButton1`, `IconOnlyButton`, `TextOnlyButton`

### 面板 (Panel)
- **Figma元素类型**: `FRAME`
- **命名规则**: 名称中必须包含 `"panel"` (不区分大小写)
- **示例**: `SettingsPanel`, `ScorePanel`, `GamePanel`

### 文本 (Text)
- **Figma元素类型**: `TEXT`
- **命名规则**: 任意名称
- **示例**: `GameTitle`, `ScoreText`, `QuestionText`

### 9-切片图像 (9-Slice Image)
- **Figma元素类型**: `RECTANGLE`
- **命名规则**: 名称中必须包含 `"9slice"` 或 `"nine"` (不区分大小写)
- **示例**: `NineSlicePanel`, `ScalableBackground`

## 工作原理

1. 使用 REST API 获取 Figma 文件数据。
2. 解析文档结构，支持多页面文件：
    - 遍历所有页面 (CANVAS)
    - 在每个页面中查找框架 (FRAME) 作为场景
3. 为每个场景生成 TypeScript 代码，以编程方式创建 UI 元素。
4. 将 Figma 元素映射到 Cocos Creator 组件：
    - 矩形/椭圆形 → Sprite 组件 + 自动生成图像
    - 文本 → Label 组件
    - 名称包含 "button" 的框架 → Button 组件
    - 名称包含 "panel" 的框架 → Panel 容器
    - 名称包含 "9slice"/"nine" 的矩形 → 9-切片 Sprite
    - 带有图像填充的矩形/椭圆形 → 图像 Sprite + TODO 加载注释

## 自动图像生成功能

工具现在支持为没有图像填充的 RECTANGLE 和 ELLIPSE 元素自动生成简单形状的 PNG 图像：

### 支持的形状类型
- **矩形 (Rectangle)**: 生成纯色矩形图像
- **椭圆形 (Ellipse)**: 生成纯色圆形/椭圆形图像
- **圆角矩形 (Rounded Rectangle)**: 生成带圆角的矩形图像

### 图像生成规则
- 从 Figma 元素的填充颜色中提取背景色
- 支持透明度和 RGB 颜色
- 自动在图像中央渲染元素名称作为文本标签
- 使用系统Arial字体，自动调整字体大小以适应图像尺寸
- 图像文件名格式:
  - 无文本: `shape_widthxheight_color.png`
  - 有文本: `shape_widthxheight_color_text_elementName.png`
- 自动保存到项目的 `assets` 目录

### 示例生成的代码
```typescript
// Create LevelBackground
const element0 = new Node('LevelBackground');
element0.setPosition(0, 0, 0);
const transform0 = element0.addComponent(UITransform);
transform0.setContentSize(800, 600);
const sprite0 = element0.addComponent(Sprite);
// Generated image with text: rect_800x600_FFFFFF_text_LevelBackground.png
// Load generated image
resources.load('textures/generated/rect_800x600_FFFFFF_text_LevelBackground/spriteFrame', SpriteFrame).then((spriteFrame) => {
  sprite0.spriteFrame = spriteFrame;
}).catch((error) => {
  console.error('Failed to load sprite frame:', error);
});
canvas.addChild(element0);
```

### 优势
- ✅ 无需手动创建图像文件
- ✅ 自动匹配 Figma 设计中的颜色
- ✅ 支持多种形状类型
- ✅ 自动渲染元素名称作为文本标签，便于识别和调试
- ✅ 生成的图像可直接用于 Cocos Creator 项目

## 限制

- 目前仅生成基本形状和文本的代码。
- 尚未实现资产下载。
- 需要手动将生成的代码集成到 Cocos Creator 项目中。

## 未来改进

- 添加资产下载和导入功能。
- 支持更多 Figma 元素类型。
- 生成实际的场景文件而不是代码。