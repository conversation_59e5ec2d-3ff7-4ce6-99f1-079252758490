import { _decorator, Component, Node, Sprite, Label, Button, UITransform, resources, SpriteFrame, Color } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GeneratedScene')
export class GeneratedScene extends Component {

  start() {
    this.createUI();
  }

  createUI() {
    const canvas = this.node;

    // Create LevelBackground
    const element0 = new Node('LevelBackground');
    element0.setPosition(0, 0, 0);
    const transform0 = element0.addComponent(UITransform);
    transform0.setContentSize(800, 600);
    const sprite0 = element0.addComponent(Sprite);
    // Generated image: rect_800x600_FFFFFF_text_LevelBackground.png
    // Load generated image
    resources.load('textures/generated/rect_800x600_FFFFFF_text_LevelBackground/spriteFrame', SpriteFrame).then((spriteFrame) => {
      sprite0.spriteFrame = spriteFrame;
    }).catch((error) => {
      console.error('Failed to load sprite frame:', error);
    });
    canvas.addChild(element0);

    // Create LevelTitle
    const element1 = new Node('LevelTitle');
    element1.setPosition(350, 50, 0);
    const transform1 = element1.addComponent(UITransform);
    transform1.setContentSize(100, 30);
    const label1 = element1.addComponent(Label);
    label1.string = '第一关';
    label1.fontSize = 24;
    label1.color = new Color(0, 0, 0, 255);
    canvas.addChild(element1);

    // Create QuestionText
    const element2 = new Node('QuestionText');
    element2.setPosition(350, 150, 0);
    const transform2 = element2.addComponent(UITransform);
    transform2.setContentSize(100, 30);
    const label2 = element2.addComponent(Label);
    label2.string = '1 + 1 = ?';
    label2.fontSize = 24;
    label2.color = new Color(0, 0, 0, 255);
    canvas.addChild(element2);

    // Create AnswerButton1
    const element3 = new Node('AnswerButton1');
    element3.setPosition(300, 250, 0);
    const transform3 = element3.addComponent(UITransform);
    transform3.setContentSize(80, 50);
    const button3 = element3.addComponent(Button);
    // TODO: Add button click event handler

    // Button child: Answer1Text
    const element3Child0 = new Node('Answer1Text');
    element3Child0.setPosition(30, 15, 0);
    const childTransform3_0 = element3Child0.addComponent(UITransform);
    childTransform3_0.setContentSize(20, 20);
    const childLabel3_0 = element3Child0.addComponent(Label);
    childLabel3_0.string = '2';
    childLabel3_0.fontSize = 20;
    childLabel3_0.color = new Color(255, 255, 255, 255);
    element3.addChild(element3Child0);
    canvas.addChild(element3);

    // Create AnswerButton2
    const element4 = new Node('AnswerButton2');
    element4.setPosition(420, 250, 0);
    const transform4 = element4.addComponent(UITransform);
    transform4.setContentSize(80, 50);
    const button4 = element4.addComponent(Button);
    // TODO: Add button click event handler

    // Button child: Answer2Text
    const element4Child0 = new Node('Answer2Text');
    element4Child0.setPosition(30, 15, 0);
    const childTransform4_0 = element4Child0.addComponent(UITransform);
    childTransform4_0.setContentSize(20, 20);
    const childLabel4_0 = element4Child0.addComponent(Label);
    childLabel4_0.string = '3';
    childLabel4_0.fontSize = 20;
    childLabel4_0.color = new Color(255, 255, 255, 255);
    element4.addChild(element4Child0);
    canvas.addChild(element4);

    // Create ScorePanel
    const element5 = new Node('ScorePanel');
    element5.setPosition(50, 50, 0);
    const transform5 = element5.addComponent(UITransform);
    transform5.setContentSize(150, 80);
    // Panel container - add background if needed
    const panelSprite5 = element5.addComponent(Sprite);

    // Panel child: ScoreText
    const element5Child0 = new Node('ScoreText');
    element5Child0.setPosition(10, 10, 0);
    const childTransform5_0 = element5Child0.addComponent(UITransform);
    childTransform5_0.setContentSize(80, 20);
    const childLabel5_0 = element5Child0.addComponent(Label);
    childLabel5_0.string = '分数: 100';
    childLabel5_0.fontSize = 24;
    childLabel5_0.color = new Color(0, 0, 0, 255);
    element5.addChild(element5Child0);

    // Panel child: StarIcon
    const element5Child1 = new Node('StarIcon');
    element5Child1.setPosition(10, 35, 0);
    const childTransform5_1 = element5Child1.addComponent(UITransform);
    childTransform5_1.setContentSize(20, 20);
    const childSprite5_1 = element5Child1.addComponent(Sprite);
    // TODO: Load panel image from 'star.png'
    element5.addChild(element5Child1);
    canvas.addChild(element5);

  }
}
