import { _decorator, Component, Node, Sprite, Label, Button, UITransform, resources, SpriteFrame, Color } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GeneratedScene')
export class GeneratedScene extends Component {

  start() {
    this.createUI();
  }

  createUI() {
    const canvas = this.node;

    // Create BackgroundImage
    const element0 = new Node('BackgroundImage');
    element0.setPosition(0, 0, 0);
    const transform0 = element0.addComponent(UITransform);
    transform0.setContentSize(800, 600);
    const sprite0 = element0.addComponent(Sprite);
    // TODO: Load image from 'background.png'
    canvas.addChild(element0);

    // Create GameTitle
    const element1 = new Node('GameTitle');
    element1.setPosition(300, 100, 0);
    const transform1 = element1.addComponent(UITransform);
    transform1.setContentSize(200, 50);
    const label1 = element1.addComponent(Label);
    label1.string = '儿童学习游戏';
    label1.fontSize = 24;
    label1.color = new Color(0, 0, 0, 255);
    canvas.addChild(element1);

    // Create PlayButton
    const element2 = new Node('PlayButton');
    element2.setPosition(350, 250, 0);
    const transform2 = element2.addComponent(UITransform);
    transform2.setContentSize(100, 50);
    const button2 = element2.addComponent(Button);
    // TODO: Add button click event handler

    // Button child: ButtonBackground
    const element2Child0 = new Node('ButtonBackground');
    element2Child0.setPosition(0, 0, 0);
    const childTransform2_0 = element2Child0.addComponent(UITransform);
    childTransform2_0.setContentSize(100, 50);
    const childSprite2_0 = element2Child0.addComponent(Sprite);
    element2.addChild(element2Child0);

    // Button child: ButtonText
    const element2Child1 = new Node('ButtonText');
    element2Child1.setPosition(20, 15, 0);
    const childTransform2_1 = element2Child1.addComponent(UITransform);
    childTransform2_1.setContentSize(60, 20);
    const childLabel2_1 = element2Child1.addComponent(Label);
    childLabel2_1.string = '开始游戏';
    childLabel2_1.fontSize = 20;
    childLabel2_1.color = new Color(255, 255, 255, 255);
    element2.addChild(element2Child1);
    canvas.addChild(element2);

    // Create IconOnlyButton
    const element3 = new Node('IconOnlyButton');
    element3.setPosition(470, 250, 0);
    const transform3 = element3.addComponent(UITransform);
    transform3.setContentSize(50, 50);
    const button3 = element3.addComponent(Button);
    // TODO: Add button click event handler

    // Button child: ButtonIcon
    const element3Child0 = new Node('ButtonIcon');
    element3Child0.setPosition(0, 0, 0);
    const childTransform3_0 = element3Child0.addComponent(UITransform);
    childTransform3_0.setContentSize(50, 50);
    const childSprite3_0 = element3Child0.addComponent(Sprite);
    // TODO: Load button background image from 'play_icon.png'
    element3.addChild(element3Child0);
    canvas.addChild(element3);

    // Create TextOnlyButton
    const element4 = new Node('TextOnlyButton');
    element4.setPosition(540, 250, 0);
    const transform4 = element4.addComponent(UITransform);
    transform4.setContentSize(80, 40);
    const button4 = element4.addComponent(Button);
    // TODO: Add button click event handler

    // Button child: ButtonLabel
    const element4Child0 = new Node('ButtonLabel');
    element4Child0.setPosition(10, 10, 0);
    const childTransform4_0 = element4Child0.addComponent(UITransform);
    childTransform4_0.setContentSize(60, 20);
    const childLabel4_0 = element4Child0.addComponent(Label);
    childLabel4_0.string = '设置';
    childLabel4_0.fontSize = 20;
    childLabel4_0.color = new Color(255, 255, 255, 255);
    element4.addChild(element4Child0);
    canvas.addChild(element4);

    // Create SettingsPanel
    const element5 = new Node('SettingsPanel');
    element5.setPosition(50, 400, 0);
    const transform5 = element5.addComponent(UITransform);
    transform5.setContentSize(200, 150);
    // Panel container - add background if needed
    const panelSprite5 = element5.addComponent(Sprite);

    // Panel child: PanelBackground
    const element5Child0 = new Node('PanelBackground');
    element5Child0.setPosition(0, 0, 0);
    const childTransform5_0 = element5Child0.addComponent(UITransform);
    childTransform5_0.setContentSize(200, 150);
    const childSprite5_0 = element5Child0.addComponent(Sprite);
    element5.addChild(element5Child0);

    // Panel child: SettingsTitle
    const element5Child1 = new Node('SettingsTitle');
    element5Child1.setPosition(20, 20, 0);
    const childTransform5_1 = element5Child1.addComponent(UITransform);
    childTransform5_1.setContentSize(80, 25);
    const childLabel5_1 = element5Child1.addComponent(Label);
    childLabel5_1.string = '游戏设置';
    childLabel5_1.fontSize = 24;
    childLabel5_1.color = new Color(0, 0, 0, 255);
    element5.addChild(element5Child1);

    // Panel child: VolumeIcon
    const element5Child2 = new Node('VolumeIcon');
    element5Child2.setPosition(20, 60, 0);
    const childTransform5_2 = element5Child2.addComponent(UITransform);
    childTransform5_2.setContentSize(30, 30);
    const childSprite5_2 = element5Child2.addComponent(Sprite);
    // TODO: Load panel image from 'volume_icon.png'
    element5.addChild(element5Child2);

    // Panel child: VolumeText
    const element5Child3 = new Node('VolumeText');
    element5Child3.setPosition(60, 65, 0);
    const childTransform5_3 = element5Child3.addComponent(UITransform);
    childTransform5_3.setContentSize(40, 20);
    const childLabel5_3 = element5Child3.addComponent(Label);
    childLabel5_3.string = '音量';
    childLabel5_3.fontSize = 24;
    childLabel5_3.color = new Color(0, 0, 0, 255);
    element5.addChild(element5Child3);
    canvas.addChild(element5);

    // Create CharacterImage
    const element6 = new Node('CharacterImage');
    element6.setPosition(500, 200, 0);
    const transform6 = element6.addComponent(UITransform);
    transform6.setContentSize(150, 200);
    const sprite6 = element6.addComponent(Sprite);
    // TODO: Load image from 'character.png'
    canvas.addChild(element6);

    // Create NineSlicePanel
    const element7 = new Node('NineSlicePanel');
    element7.setPosition(550, 450, 0);
    const transform7 = element7.addComponent(UITransform);
    transform7.setContentSize(200, 100);
    const sprite7 = element7.addComponent(Sprite);
    // TODO: Load image from 'panel_9slice.png'
    // Configure as 9-slice sprite
    const spriteFrame7 = sprite7.spriteFrame;
    if (spriteFrame7) {
      spriteFrame7.insetLeft = 20;
      spriteFrame7.insetRight = 20;
      spriteFrame7.insetTop = 10;
      spriteFrame7.insetBottom = 10;
    }
    canvas.addChild(element7);

  }
}
