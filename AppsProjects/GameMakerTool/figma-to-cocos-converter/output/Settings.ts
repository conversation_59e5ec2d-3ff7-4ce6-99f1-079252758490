import { _decorator, Component, Node, Sprite, Label, Button, UITransform } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GeneratedScene')
export class GeneratedScene extends Component {

  start() {
    this.createUI();
  }

  createUI() {
    const canvas = this.node;

    // Create SettingsTitle
    const element0 = new Node('SettingsTitle');
    element0.setPosition(350, 50, 0);
    const transform0 = element0.addComponent(UITransform);
    transform0.setContentSize(100, 30);
    const label0 = element0.addComponent(Label);
    label0.string = '设置';
    canvas.addChild(element0);

  }
}
